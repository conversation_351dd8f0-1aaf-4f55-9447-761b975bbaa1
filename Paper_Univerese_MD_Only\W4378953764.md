# Translation of the efficacy of antibody–drug conjugates from preclinical to clinical using a

## Abstract
<!-- PARAGRAPH_START [abstract] -->Three semimechanistic pharmacokinetic/pharmacodynamic (PK/PD) models, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and Hybrid, were used for the efficacy translation of RC88 from preclinical to clinical. RC88 is a mesothelin‐targeting antibody–drug conjugate for malignant solid tumor. In the preclinical study, the relationship between PKs and PDs was determined using the xenograft mouse model derived from ovarian cancer and lung cancer cell lines. A secondary parameter representing the efficacy index of the drug, termed as tumor static concentration (TSC), was calculated using the three semimechanistic PK/PD models. A mechanism‐based target‐mediated drug disposition model was used to predict the human PKs. TSC from mice and predicted human PK were integrated to predict human efficacy dose. Results showed that 2 cell lines were sensitive to drugs, and the predicted efficacy dose was between 0.82 and 1.96 mg/kg q1w.

## INTRODUCTION

<!-- PARAGRAPH_START [direct] -->Antibody–drug conjugates (ADCs), a family of targeted therapeutic agents for cancer treatment, are designed to be a monoclonal antibody conjugated to a chemical cytotoxic drug (known as payload) via a chemical linker. ADCs can improve the therapeutic index of cytotoxic agents by restricting their systemic delivery to cells that express the target antigen of interest.
1
, 
2
, 
3
, 
4
 Although ADCs are promising, they often have some clinical challenges.
5
 Several recent clinical trials have shown that the window between efficacy and toxicity of ADCs is narrow.
6
, 
7
 Dose‐limiting toxicities were often seen in patients even before an efficacy dose could be identified, leading to the early termination of the ADC program.
8
 Therefore, determining an appropriate dosage range can save cost and time and is extremely important for the development of ADCs.

<!-- PARAGRAPH_START [direct] -->In recent years, various pharmacokinetic/pharmacodynamic (PK/PD) mathematical models have been built for use in the translation of preclinical efficacy.
9
, 
10
, 
11
 Such models can integrate data generated from diverse test platforms in a mechanistic framework to describe the relationship among dose, exposure, and response/safety.
12
 Tumor growth inhibition (TGI) in xenograft mouse models are typically used to assess the response of oncology drugs preclinically.
13
 Empirical models use mathematical equations to describe the tumor growth curve and are widely used because of their simplicity and parsimony. However, changes of the parameters depend on the dose level and administration schedule, such that these approaches can only be applied retrospectively and not as predictive tools when used outside the tested regimens.
14
 Full‐fledged mechanistic models can achieve accurate translation of preclinical efficacy to humans, but required data are often not generated in the discovery stage, limiting the application of highly mechanistic models. Semimechanistic models correlate the tumor cells killing with drug concentration in serum and incorporate the transit compartments to account the delay between the dose and pharmacological effect. Only PK and in vivo tumor growth/regression data were required to estimate parameters in preclinical studies. Accordingly, they offer a compromise between empirical PK/PD and full‐fledged mechanistic models to guide drug discovery and development.
15
 Considering system‐specific properties insufficient preclinical data, semimechanistic models are an appropriate model for clinical translation in the early clinical stage of drugs.

<!-- PARAGRAPH_START [direct] -->Although some semimechanistic PK/PD models are used as a tool to translate clinical efficacy, the different hypotheses of the tumor growth model and tumor cell‐killing function can affect the predicted result. There are few reports on the translation clinical efficacy that used multiple PK/PD models and multiple tumor cell models. In the present paper, three semimechanistic PK/PD models were used to translate mouse TGI data to predict the dose for clinical study, and the prediction results of the three models were compared to improve the accuracy of model predictions. TGI data from ovarian cancer and lung cancer cell‐derived xenograft (CDX) models treated with RC88 and serum PK data from mouse and monkey subjects were used to predict the clinical efficacy. RC88 is a novel anti‐mesothelin ADC consisting of a humanized anti‐mesothelin monoclonal antibody conjugated microtubule inhibitor, monomethyl auristatin E (MMAE). Mouse TGI data were modeled using the three semimechanistic models to determine tumor static concentration (TSC). The preclinical efficacy was subsequently translated to humans to predict efficacious clinical dose.

## MATERIALS AND METHODS

### Experimental methods

#### Cell lines

<!-- PARAGRAPH_START [direct] -->The cell lines OVCAR‐3 (ovarian cancer) and H292 (lung cancer) were chosen because they exhibited reproducible growth curves as tumor xenografts. These cell lines were obtained from American Type Culture Collection (ATCC). OVCAR3‐MSLN‐3# and H292‐MSLN‐9C8 cell lines were generated from the control cell lines after lentiviral transfection of MSLN by RemeGen Co., Ltd.

#### Tool

<!-- PARAGRAPH_START [direct] -->RC88, developed by RemeGen Co., Ltd., was synthesized and characterized in‐house. Humanized IgG1 monoclonal antibody targeting MSLN was conjugated with valine–citrulline MMAE (drug‐linker solution) using a random conjugation method. The average drug antibody ratio (DAR) was ∼4. Related studies have shown that RC88 cross reaction with monkeys' but not rodents' MSLN.

#### Animals

<!-- PARAGRAPH_START [direct] -->Female severe nude mice (Balb/c nude) ages 4–5 weeks were purchased from either Shanghai Laboratory Animal Co., Ltd. (Shanghai, China) or Changzhou Calvin Laboratory Animals Limited. The license numbers were SCXK Hu SCXK (Hu) 2012 0002 and SCXK (Su) 2016–0010. Animals were maintained in an individual ventilated cage room with sterilized food and purified water. They have free access to food and water for 12 h light/12 h dark. All the animal experiments were conducted in accordance with current practices and ethical principles.

#### Mice

<!-- PARAGRAPH_START [direct] -->Balb/c nude mice were implanted subcutaneously with OVCAR3‐MSLN‐3# or H292‐MSLN‐9C8 cells (i.e., 3 million OVCAR3‐MSLN‐3# tumor cells [transfected ovarian cancer cell line] or 3 million H292‐MSLN‐9C8 tumor cells [transfected lung cancer cell line]). When the volume of subcutaneous xenografts reached about 250 mm3, the animals were randomly divided into different groups to conduct the PD studies. The dosage regimen is detailed in Table 1. Animals (6 mice/group) with staged (~250 mm3) tumors were intravenously administered saline (vehicle) or RC88. Tumors were measured twice a week. Tumor volume was calculated as mm3 = 0.5 × (tumor width2) × (tumor length).

<!-- PARAGRAPH_START [nested] -->Dosing regimen for RC88.

<!-- PARAGRAPH_START [nested] -->Abbreviations: CDX, cell‐derived xenograft; M, multiple doses; S, single dose.

#### Mice

<!-- PARAGRAPH_START [direct] -->The PKs of RC88 were determined along with single‐dose PD in OVCAR3‐MSLN‐3# tumor‐bearing mice. The dosage regimen followed that of OVCAR3‐MSLN‐3# (S) as shown in Table 1. At 0.083, 4, 12, 24, 48, 72, 96, 120, 168, and 240 h after RC88 administration, three animals from each group were euthanized to collect blood samples, which were immediately processed to extract serum. All the serum samples were stored at −80°C until further analysis.

#### Cynomolgus monkey

<!-- PARAGRAPH_START [direct] -->A total of 18 cynomolgus monkeys were equally divided into three groups, which were treated intravenously with a single 0.5, 1.5, and 5 mg/kg (n = 6/group, half male and half female) dose of RC88, respectively. Blood samples were collected predose and at 0.083, 0.25, 2, 6, 24, 48, 72, 96, 168, 336, 504, and 672 h postdose. Blood samples were immediately processed to extract serum, and all the serum samples were stored at −80°C until further analysis.

#### 

<!-- PARAGRAPH_START [direct] -->In the early study, we carried out in vitro cytotoxicity study, and the results showed that naked antibodies had no killing effect on tumor cells, but ADC had a strong killing effect, so ADC was used as the driver for modeling in the study. The ADC concentrations in the Balb/c nude mouse and cynomolgus monkey serum were determined using enzyme‐linked immunosorbent assay detection. For the RC88 assay, the capture protein was a mouse anti‐MMAE antibody. The detection reagent was a goat anti‐human Fc conjugated to horseradish peroxidase (HRP) in Balb/c nude mouse serum and goat anti‐human IgG(H + L) conjugated to HRP in cynomolgus monkey serum. 3,3′,5,5′‐tetramethylbenzidine was used as the substrate for a colorimetric readout. The change in absorbance was measured over time (dA/dt) at 450 nm using Spectra Max M5 microplate analyzer (MD). The standard curve was fitted using a four‐parameter equation (SoftMax Pro software).

### 

<!-- PARAGRAPH_START [direct] -->The Michaels–Menten (M–M) nonlinear two‐compartment PK model was used to described mouse PK. The target mediated drug disposition (TMDD) model was used to described monkey PKs, as shown in Figure 1c. These PK models have been introduced preciously.
16
, 
17
 The transduction PD model was used to describe mouse tumor cell killing using ADC serum PK as an efficacy driver. The linear and nonlinear tumor cell kill functions was used. The tumor cell killing in each compartment was subsequently transferred from the prior compartment to the next compartment through the first‐order rate constant (t), as shown in Figure 1b. Three tumor growth models were used, namely the Hybrid model,
15
 the Simeoni model,
14
 and the Jumbe model.
18

#### Pharmacokinetics of

<!-- PARAGRAPH_START [direct] -->The serum concentration versus time profiles in mice were captured by an M–M nonlinear two‐compartment model, which has parallel linear (clearance [CL]) and M–M (maximal rate of metabolism/(C + K
m)) elimination in the central compartment (Figure 1a). The serum concentrations versus time profiles in monkeys were captured by a TMDD model, as shown in Figure 1c. ADC in the central compartment binds to receptors (R) at the second‐order rate (K
on) to form a drug receptor complex. The complex may dissociate at the first‐order rate (K
off) or be internalized and degraded at the first‐order rate process of endocytosis (K
e). ADC can also be eliminated via two‐compartment. ADC can also be directly eliminated from the central compartment (CL/V1) or be distributed to the peripheral compartment (CLD/V2).

#### Tumor growth inhibition

<!-- PARAGRAPH_START [direct] -->The mouse xenograft PK/PD relationship was established by relating the RC88 serum concentration to the measured tumor volume (Figure 1b). The mouse PK parameters derived above were fixed in the subsequent PD modeling of the xenograft mouse data. Three tumor growth models were used to describe the efficacy of RC88, namely, the Simeoni model (Equation 1),
14
 the Hybrid model (Equation 2),
15
 and the Jumbe model (Equation 3).
18
 The model equations for the above three models were as follows:
Kg=λ0\*E11+λ0λ1\*E1ψ1ψ

dE1tdt=Kg−K2\*C\*E1t

dE2tdt=K2\*C\*E1−K1\*E2t

dE3tdt=K1\*E2t−E3t

dE4tdt=K1\*E3t−E4t

(1)
E=E1+E2+E3+E4

Kg=KgEx\*1−EEmax1+KgExKgrow\*Eψ1ψ

Kkill=Kmax\*CtKC50+Ct

dE1tdt=Kg−Kkill\*E1t

dE2tdt=Kkill\*E1t–1t\*E2t

dE3tdt=1t\*E2t–1t\*E3t

dE4tdt=1t\*E3t–1t\*E4t

(2)
E=E1+E2+E3+E4

Kkill=Kmax\*CtKC50+Ct

dE1tdt=Kgrow−1t\*Kkill\*E1t2/3)

dE2tdt=Kkill\*1t\*E1t2/3−1t\*E2t2/3

dE3tdt=1t\*E2t2/3–1t\*E3t2/3

dE4tdt=1t\*E3t2/3–1t\*E4t2/3

(3)
E=E1+E2+E3+E4

<!-- PARAGRAPH_START [direct] -->The measured initial tumor volume in each animal was used for the initial conditions (E1). E1–E4 were the tumor volume in the growth compartment and three transduction compartments, respectively. E was the total tumor volume (mm3).

#### Efficacy index: Tumor static concentration

<!-- PARAGRAPH_START [direct] -->Tumor response was dependent on ADC concentration in this PK/PD model. The secondary parameter, TSC, could be calculated by the PK/PD model, which means tumor growth and death rates were instantaneously equal and the tumor volume remained unchanged. This secondary parameter combined the tumor growth information and drug effect, thus providing insight into the efficacy of the ADC, which can be used for translation by extrapolating xenograft data to the clinic. The dosing interval troughs should exceed this concentration in order for the effective tumor kill rate to exceed tumor growth rates for net antitumor activity.

<!-- PARAGRAPH_START [direct] -->For the three models, TSC can be calculated as:
TSC=λ0K2Simeoni model

TSC=Kgrow\*KC50Kkill\*Kmax−KgJumbe model

(4)
TSC=KgEx\*KC501−EEmaxKmax1+KgExKgrow\*Eψ1ψ−KgEx1−EEmaxHybrid model

### Modeling

<!-- PARAGRAPH_START [direct] -->All modeling was performed using Phoenix NLME software (version ********; Certara) with the FOCE‐ELS method, and model evaluation was based on a bootstrap run of 1000 predictions of model estimates. For the choice of structural model, a parameter or a covariate was considered to have significant improvement in the model if it reduced the minus two times the log‐likelihood function by more than 3.84 (p ≤ 0.05 based on χ2 distribution). Based on exploratory analyses, the concentration–efficacy relationship between RC88 and tumor volume was explored using various PK/PD models.

### Clinical

<!-- PARAGRAPH_START [direct] -->Monkeys have high homology with humans, and the stability and metabolic pathways of ADC in vivo are similar, so the monkey PKs were used to clinical translation for the TMDD model‐based prediction of human PKs. The allometric exponents used for drug‐specific model parameters (e.g., CL, CLD, V1, and V2) were denoted as 1. For monoclonal antibody, 0.90 is a more appropriate exponent, mainly affected by the target‐mediated disposition and whether it is linear, but for ADC drugs, although similar to monoclonal antibody, it is also affected by the deconjugation rate of the payload. Combined with the ADC drug prediction results currently on the market, namely, TDM‐1 and brentuximab vedotin, and one ADC in phase III, namely, inotuzumab ozogamicin. The scaling exponent of 1 was a better predictor.
15
, 
19
, 
20
 The target‐specific model parameters (e.g., K
out, K
on, K
off, and K
e) were scaled 1:1 between monkeys and humans. We assumed that receptor expression parameter (R) was found to be similar between the monkeys and healthy humans.
21
 Data indicated three‐fold higher MSLN baseline levels in disease state compared to healthy humans,
22
 so R was scaled 3:1 between monkeys and humans.

## RESULTS

### In vivo xenograft tumor response

#### 

<!-- PARAGRAPH_START [direct] -->Efficacy was evaluated in experimental tumors arising from the implantation of OVCAR3‐MSLN‐3# cell lines expressing high levels of MSLN by single and multiple intravenous (i.v.) administrations with RC88. Animals were dosed intravenously with a single dose of RC88 at 0.7 and 2.1 mg/kg, as shown in Figure 2a. Animals were dosed intravenously with RC88 at 0.75, 1.5, and 3 mg/kg q1w × 3 (every week for 3 cycles) as shown in Figure 2b. The dose‐dependent antitumor activity of RC88 was demonstrated in both experiments.

<!-- PARAGRAPH_START [direct] -->With single dosing, moderate activity was observed at 0.75 mg/kg, but tumor volume was less than the initial volume of tumors treated at 2.1 mg/kg until 11 days after dosing. With multiple dosing, the tumors were significantly reduced 7 days after the first dosing at 1.5 and 3 mg/kg. Tumors were fully regressed at 3 mg/kg for 10 days and at 1.5 mg/kg for 17 days, but the tumor continued to grow after three administrations at 0.75 mg/kg.

#### 

<!-- PARAGRAPH_START [direct] -->Efficacy was evaluated in experimental tumors arising from the implantation of H292‐MSLN‐9C8 cell lines expressing high levels of MSLN by multiple i.v. administrations with RC88. Animals were dosed with RC88 i.v. at 0.75, 1.5, and 3 mg/kg q1w × 3 cycle, as shown in Figure 2c. The dose‐dependent antitumor activity of RC88 was demonstrated for the concentration versus time profiles of RC88. Moderate activity was observed at 0.75 and 1.5 mg/kg, but tumor volume treated at 3 mg/kg regressed to less than 201 mm3.

#### Serum

<!-- PARAGRAPH_START [direct] -->To evaluate the PK/PD relationship in mice, PKs were determined separately following a single i.v. administration of RC88 at 0.7 and 2.1 mg/kg. To inform RC88 clinical PK predictions, PKs were determined in the cynomolgus monkey following a single i.v. administration at 0.5, 1.5, and 3 mg/kg. The PKs of RC88 in mice was characterized using the M–M nonlinear two‐compartment model. The PKs of RC88 in monkeys were characterized using the TMDD model. The exposure parameters are present in Table S1. The model fittings are shown in Figure 3a,b. For visual predictive checks for each PK model, please see Figure S1. The result showed that the median response and variability were described well by the PK model. The parameter estimates are presented in Table 2. Human‐predicted PK parameters for RC88, scaled based on monkey PK parameters, are also shown in Table 2.

<!-- PARAGRAPH_START [nested] -->Estimated or scaled PK parameters.

<!-- PARAGRAPH_START [nested] -->Abbreviation: PK, pharmacokinetic.

<!-- PARAGRAPH_START [nested] -->%RSE for parameter estimates are derived from bootstrap analyses.

<!-- PARAGRAPH_START [nested] -->Central volume; final parameter estimates and their relative standard error (RSE %) were provided.

### 

#### Model comparison

<!-- PARAGRAPH_START [direct] -->The Simeoni, Jumbe, and Hybrid models were used to fit RC88 TGI data measured in‐house with two experimental tumor cell lines (OVCAR3‐MSLN‐3# and H292‐MSLN‐9C8). The calculated TSC and Bayesian information criterion and Akaike information criterion for the three models are given in Table 3. All three models can capture PD characteristics, except for OVCAR3‐MSLN‐3# (M), where the estimation using the Jumbe model failed to converge. This outcome may be attributable to only the exponential growth model that differs from the actual growth of the tumor. We modeled OVCAR3‐MSLN‐3# (S) and OVCAR3‐MSLN‐3# (M) separately, and compared the results of secondary parameters. The purpose is to validate the model. The result showed that the difference in TSC is small. These models could be used to predict the characteristics of PK/PD. Details on the model parameter estimates for each PK/PD model are also given in Tables S2, S3, and S4. Owing to the diversity between the model structures, a direct comparison of parameter estimates was not possible.

<!-- PARAGRAPH_START [direct] -->The quality of the fits and model performance were assessed using the goodness‐of‐fit plots and visual predictive check plots for the Jumbe, Simeoni, and Hybrid models, which are given in Figures S2, S3, and S4. The estimated TSCs were found to vary depending on the type of CDX model.

<!-- PARAGRAPH_START [nested] -->Comparison of models.

<!-- PARAGRAPH_START [nested] -->Note: Values in the bracket are 90% confidence interval of TSC.

<!-- PARAGRAPH_START [nested] -->Abbreviations: AIC, Akaike Information Criterion; BIC, Bayesian Information Criterion; CDX, cell‐derived xenograft; TSC, tumor static concentration.

<!-- PARAGRAPH_START [nested] -->The number of successful runs during the bootstrap process n = 1000.

### Clinical translation

<!-- PARAGRAPH_START [direct] -->To predict the clinically efficacy dose of RC88, the mouse PK parameters were replaced by predicted human PK. The clinical dose prediction using the two experimental tumors is shown in Table 4. In preclinical studies, the number of animals and the variation between individuals were inevitable challenges, so the stability and accuracy of the model parameters were affected by these challenges. We attempted to improve the accuracy of predictions by running multiple PD models with different hypotheses and comparing the differences in results. The result showed that the typical value difference of TSC calculated by different PD models is within two‐folds. We think the typical values of parameter could be accepted, although the %RSE of the parameter estimation was large, and the variation between animals did not reflect the variation between humans. Therefore, we only used typical values of parameter to predict the human dose. The dose predictions were based on a stable disease (TSC) with a q1w dosing regimen. Tumor stasis predicted doses were between 0.82 and 1.96 mg/kg q1w based on the two experimental tumors.

<!-- PARAGRAPH_START [nested] -->The median and range of TSC values and predicted clinical dose using two cell lines form three PK/PD model.

<!-- PARAGRAPH_START [nested] -->Note: OVCAR3‐MSLN‐3# including single dose and multiple doses.

<!-- PARAGRAPH_START [nested] -->Abbreviations: PD, pharmacodynamic; PK, pharmacokinetic; TSC, tumor static concentration.

## DISCUSSION

<!-- PARAGRAPH_START [direct] -->The ADCs have complex mechanisms of action, and many factors affect their efficacy, including binding, receptor number, internalization rate, payload cellular trafficking, and DAR kinetics. The use of the PK/PD model is particularly important for ADCs, and, on this basis, various PK/PD models of ADC have been built.
21
, 
23
, 
24
, 
25
 Although some PK/PD models have been reported in the literature, the selection of the model needs to be determined based on the drug type, mechanism of action, and data available in preclinical. In the early study, the results of in vitro cytotoxicity experiments showed that naked antibodies had no killing effect on tumor cells, but ADC had a strong killing effect. The mechanism of action studies of RC88 has shown that upon binding to MSLN on the surface of tumor cells, it undergoes receptor‐mediated internalization and trafficking from the endosomes to lysosomes. In addition, the cytotoxic MMAE is released by protease cleavage to exert a killing efficiency and arrest the tumor cell cycle in the G2/M phase, thereby inducing apoptosis.
26
, 
27
 This finding also suggests that the efficacy of the ADC bearing payload is closely related to the growth rate of the tumor. The concentration of ADC in serum could be measured directly, but tumor distribution studies are costly and time‐consuming. Although the concentration of ADC in tumor tissue is responsible for eliciting the pharmacological action, but there was a correlation between the concentration of the ADC in serum and tumor tissue. The ADC in serum was used as the driver for modeling in the study.
28
 The semimechanistic PK/PD model focuses on the macroscopic description of the system and adequately captures these key parameters of drug efficacy.
15
, 
29
 Considering insufficient preclinical data to support mechanistic modeling, the semimechanistic model is an appropriate model for clinical translation.

<!-- PARAGRAPH_START [direct] -->In this study, The M–M nonlinear the two‐compartment PK model was used to describe the serum concentration versus time profiles of mice. The TMDD model‐based prediction of human PK. Although the M‐M model can describe the PK profile, it has poor predictive effect, and a mechanistic TMDD model is used in translational clinical.
30
 The semimechanistic PD model was used to parameterize the tumor growth and drug killing effect. Three semimechanistic PD models were used, namely, Simeoni, Jumbe, and Hybrid. These models describe different tumor growth models and drug kill functions. The effects of using different growth models and drug kill tumor models on simulated TSC were compared. The control animals were used to describe the tumor growth rate. The Simeoni model considered that the tumor grows exponentially up to a threshold weight, above which the growth becomes linear with time. The model parameters λ0 and λ1 provide estimates of the rates of growth in these two phases.
14
, 
31
 The Jumbe model considered that the tumor grows at a net growth rate (K
grow) following first‐order kinetics, and a spherical model was used to describe 3D tumor volume.
18
 The Hybrid model was considered a generic growth function that combines exponential, linear, and logistic functions. The parameters K
gEx and K
grow were estimated as the exponential growth rate and linear growth rate, respectively. In the treated animals, the growth was perturbed by the drug that makes some cells non‐proliferating, eventually bringing them to death.
15
 In this case, the serum ADC concentration was used as the efficacy driver. The linear model was used in the Simeoni model, and the M–M model was used in the Simeoni and Hybrid models. We modeled OVCAR3‐MSLN‐3# (S) and OVCAR3‐MSLN‐3# (M) separately, and compared the results of secondary parameters. The purpose is to externally validate the model. The result shown that the difference in TSC is small, and these models could be used to predict the characteristics of PK/PD. The result showed that three PK/PD models successfully described the experimental data, except for OVCAR3‐MSLN‐3# (M), where the estimation using the Jumbe model failed to converge. Although the results can converge, the variability of the parameters is large, and the accuracy of the model parameters is challenged. This might be affected by the number of animals in preclinical studies. To overcome this challenge, we run through multiple models in an attempt to improve the accuracy of the results by comparing the magnitude of the differences before the simulation results of different models. The difference in TSC simulated by three PK/PD models in the same animal experiment was within two‐folds. The TSC in the H292‐MSLN‐9C8 cell line was higher than that in OVCAR3‐MSLN‐3#, reflecting that the efficacy transit time was too long, which may be related to the sensitivity of the tumor microenvironment. The tumor microenvironment comprises a mass of heterogeneous cell types, including immune cells, endothelial cells, and fibroblasts, alongside cancer cells.
32
 In particular, the sensitivity of fibroblasts to drugs may differ. To improve the accuracy of model predictions, we used the TSC simulated by three PK/PD models for the subsequent guidance of the clinical dosing regimen. The dose predictions were based on a stable disease (TSC) with a q1w dosing regimen. Tumor stasis predicted doses were between 0.82 and 1.96 mg/kg q1w based on two experimental tumors. At present, RC88 is in the clinical phase I dose escalation stage for patients with solid tumors (ID Number: NCT05508334), and the prediction of this clinical dose provided a certain reference value for clinical administration. The clinical outcome will test the accuracy of model prediction. Of course, some differences between humans and animals, such as tumor growth rate, ADC stability in serum, target density, and other factors, can affect the accuracy of the model predictions.

<!-- PARAGRAPH_START [direct] -->In summary, translational PK/PD analysis can serve as a useful tool to maximize the clinical predictive value of preclinical efficacy data. We used three semimechanistic PK/PD models to simulate the RC88 TGI data and characterize the exposure–response relationship. Translation of the mouse data to the clinic was made by incorporating predictive human PK and TSC from xenograft mice. The drug is currently in clinical phase I, and more clinical efficacy will test the predicted results' modeling by the PK/PD model.

### A UTHOR CONTRIBUTIONS

<!-- PARAGRAPH_START [direct] -->Q.L., L.W., and J.J. wrote the manuscript. Q.L., L.W., and J.J. designed the research. Q.L., L.W., J.Z., G.Z., Z.L., and J.J. performed the research. Q.L., L.W., G.Z., Z.L., and J.J. analyzed the data. Q.L., L.W., J.Z., G.Z., Z.L., X.M., and J.J. contributed new reagents/analytical tools.

## FUNDING INFORMATION

<!-- PARAGRAPH_START [direct] -->This work was financially supported by Shandong Provincial Natural Science Foundation (NO. ZR2021MH220); Yantai Science and Technology Plan (NO. 2021XDHZ083).

## CONFLICT OF INTEREST STATEMENT

<!-- PARAGRAPH_START [direct] -->The authors declared no competing interests for this work.

## Supporting information

<!-- PARAGRAPH_START [nested] -->Data S1

<!-- PARAGRAPH_START [nested] -->Click here for additional data file.

## Other Content
<!-- PARAGRAPH_START [outside] -->Li
Q
, 
Wang
L
, 
Zhang
J
, et al. Translation of the efficacy of antibody–drug conjugates from preclinical to clinical using a semimechanistic PK/PD model: A case study with RC88
. Clin Transl Sci. 2023;16:1232‐1242. doi:10.1111/cts.13526

<!-- PARAGRAPH_START [outside] -->Qiaoning Li and Ling Wang are co‐first authors.

<!-- PARAGRAPH_START [outside] -->Study Highlights

WHAT IS THE CURRENT KNOWLEDGE ON THE TOPIC?

Although antibody‐drug conjugated (ADC) is promising, there are often some clinical challenges, such as low efficacy and severe dose‐limiting toxicities. Determining an appropriate dosage range is extremely important for the development of ADCs and can save cost and time.

WHAT QUESTION DID THIS STUDY ADDRESS?

Based on preclinical data, the observed dose‐exposure‐response relationship of RC88 in xenograft mouse and pharmacokinetic (PK) data in monkeys were integrated to predict human efficacious dose in clinical studies.

WHAT DOES THIS STUDY ADD TO OUR KNOWLEDGE?

This paper suggests multiple semimechanistic PK/pharmacodynamic (PD) models are used to translate preclinical data to the clinic. The difference of simulation results under different hypothesis, including different tumor growth models and different drug kill function, were explored, and to overcome the challenges of the number of animals and the variation between individuals by comparing the predicted results of the models.

HOW MIGHT THIS CHANGE CLINICAL PHARMACOLOGY OR TRANSLATIONAL SCIENCE?

This study provided a case that we can improve the reliability of model predictions by running multiple PD models with different hypotheses to maximize the clinical predictive value of preclinical efficacy data.

<!-- PARAGRAPH_START [outside] -->WHAT IS THE CURRENT KNOWLEDGE ON THE TOPIC?

<!-- PARAGRAPH_START [outside] -->Although antibody‐drug conjugated (ADC) is promising, there are often some clinical challenges, such as low efficacy and severe dose‐limiting toxicities. Determining an appropriate dosage range is extremely important for the development of ADCs and can save cost and time.

<!-- PARAGRAPH_START [outside] -->WHAT QUESTION DID THIS STUDY ADDRESS?

<!-- PARAGRAPH_START [outside] -->Based on preclinical data, the observed dose‐exposure‐response relationship of RC88 in xenograft mouse and pharmacokinetic (PK) data in monkeys were integrated to predict human efficacious dose in clinical studies.

<!-- PARAGRAPH_START [outside] -->WHAT DOES THIS STUDY ADD TO OUR KNOWLEDGE?

<!-- PARAGRAPH_START [outside] -->This paper suggests multiple semimechanistic PK/pharmacodynamic (PD) models are used to translate preclinical data to the clinic. The difference of simulation results under different hypothesis, including different tumor growth models and different drug kill function, were explored, and to overcome the challenges of the number of animals and the variation between individuals by comparing the predicted results of the models.

<!-- PARAGRAPH_START [outside] -->HOW MIGHT THIS CHANGE CLINICAL PHARMACOLOGY OR TRANSLATIONAL SCIENCE?

<!-- PARAGRAPH_START [outside] -->This study provided a case that we can improve the reliability of model predictions by running multiple PD models with different hypotheses to maximize the clinical predictive value of preclinical efficacy data.

<!-- PARAGRAPH_START [outside] -->The authors would like to thank the colleagues who completed the bioanalytical analyses and mouse experiments described in this paper. The authors would also like to thank members of the Shanghai InnoStar Bio‐tech Co., Ltd. team who completed the cynomolgus monkey experiments.

<!-- TABLE_START -->
### Dosing regimen for RC88.

| CDX Model | Dose (mg/kg) | Dosing regimen | No. of animals/group |
| --- | --- | --- | --- |
| OVCAR3‐MSLN‐3# (S) | 0.7, 2.1 | q1w × 1 | 3 |
| OVCAR3‐MSLN‐3# (M) | 0.75, 1.5, 3 | Q1W × 3 | 6 |
| H292‐MSLN‐9C8 | 0.75, 1.5, 3 | Q1W × 3 | 6 |

<!-- TABLE_START -->
### Estimated or scaled PK parameters.

| Parameter (unit) | Description | Mouse | Monkey | Human |
| --- | --- | --- | --- | --- |
| Estimate | Estimate
a | Scaled value |
| CLADC (mL/h/kg) | Clearance | 0.527 (45.9%) | 0.556 (7.9%) | 0.556 |
| CLDADC (mL/h/kg) | Intercompartmental distribution | 1.31 (29.4%) | 3.519 (16.3) | 3.519 |
| V1 (mL/kg) | Volume of the central compartment
b | 67.1 (4.97%) | 35.8 (fixed) | 35.8 |
| V2 (mL/kg) | Volume of the peripheral compartment | 49.1 (42.5%) | 16.9 (9.7%) | 16.9 |
| K
m (μg/mL) | Michaelis–Menten constant | 5.02 (78.5%) |  |  |
| V
max (μg/mL/h) | Maximum elimination rate in equation | 1.73 (131%) |  |  |
| K
on (1/μg/mL/h) | Second order association rate of ligand to receptor |  | 0.00124 (10.7%) | 0.00124 |
| K
off (1/μg/mL*h) | First‐order dissociation rate of receptor‐ligand complex |  | 0.00442 (19.3%) | 0.00442 |
| K
out (1/h) | Fractional turnover rate of receptor |  | 0.00496 (14.9%) | 0.00496 |
| K
e (1/h) | First‐order elimination rate of receptor‐ligand complex |  | 0.0154 (16.6%) | 0.0154 |
| R (μg/mL) | Initial receptor concentration |  | 36.6 (11.8%) | 109.8 |

<!-- TABLE_START -->
### Comparison of models.

| CDX model | Parameter (unit) | Simeoni model | Jumbe model | Hybrid model |
| --- | --- | --- | --- | --- |
| OVCAR3‐MSLN‐3# (S) | TSC (μg/mL) | 1.84 (0.557–3.13) | 1.22 (−1.17–3.61) | 2.32 (−0.014–4.66) |
| AIC | 2109 | 2231 | 2126 |
| BIC | 2134 | 2253 | 2146 |
| Bootstrap
a | 1000 | 1000 | 993 |
| OVCAR3‐MSLN‐3# (M) | TSC (μg/mL) | 1.95 (−1.18–5.08) | Failed | 1.37 (−1.75–4.49) |
| AIC | 1434 | 1433 |
| BIC | 1456 | 1451 |
| Bootstrap
a | 1000 | 1000 | 996 |
| H292‐MSLN‐9C8 | TSC (μg/mL) | 3.27 (−5.14–11.7) | 2.38 (1.00–3.76) | 1.31 (−0.44–3.05) |
| AIC | 1668 | 1679 | 1656 |
| BIC | 1697 | 1699 | 1676 |
| Bootstrap
a | 1000 | 1000 | 994 |

<!-- TABLE_START -->
### The median and range of TSC values and predicted clinical dose using two cell lines form three PK/PD model.

| Cell line | TSC (μg/mL) | Dosing regimen (mg/kg) |
| --- | --- | --- |
| OVCAR3‐MSLN‐3#* | 1.84 (1.22–2.32) | 1.2 (0.82–1.45) q1w |
| H292‐MSLN‐9C8 | 2.38 (1.31–3.27) | 1.5 (0.88–1.96) q1w |